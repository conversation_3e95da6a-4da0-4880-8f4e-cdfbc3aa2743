# Push Notification Scripts

This directory contains scripts for sending push notifications with Instagram-style images.

## Scripts

### 1. `send_instagram_notification.js`
Sends a single Instagram-style push notification with the title "Like Post" and description "Like our new post and get 5 zerokoin".

**Usage:**
```bash
# Using npm script
npm run send-instagram-notification "YOUR_FCM_TOKEN_HERE"

# Or directly with node
node scripts/send_instagram_notification.js "YOUR_FCM_TOKEN_HERE"
```

**Features:**
- Uses a beautiful Instagram-style image
- Sends notification with title "Like Post"
- Description: "Like our new post and get 5 zerokoin"
- Includes reward data (5 zerokoin)
- Works on both Android and iOS

### 2. `send_batch_instagram_notifications.js`
Advanced script that can send notifications to multiple users.

**Usage:**

**Option 1: Send to specific FCM tokens**
```bash
# Using npm script
npm run send-batch-instagram "token1" "token2" "token3"

# Or directly with node
node scripts/send_batch_instagram_notifications.js "token1" "token2" "token3"
```

**Option 2: Send to all users in database (requires MongoDB connection)**
```bash
npm run send-batch-instagram
```

**Features:**
- Multiple Instagram-style images (randomly selected)
- Multiple notification variations
- Batch processing with delays
- Database integration (optional)
- Success/failure reporting
- Rate limiting to avoid spam

## Image URLs Used

The scripts use high-quality Instagram-style images from Unsplash:
- Social media posts
- Lifestyle images
- Food posts
- Phone/social media themed images

## Notification Data Structure

Each notification includes:
```json
{
  "type": "instagram_like",
  "image": "https://...",
  "reward": "5",
  "action": "like_post",
  "userId": "user_id_here"
}
```

## Requirements

- Firebase Admin SDK configured
- FCM tokens from your mobile app users
- MongoDB connection (optional, for batch sending)

## Getting FCM Tokens

FCM tokens are generated by your mobile app. Users need to:
1. Install your app
2. Grant notification permissions
3. The app should send the FCM token to your backend

## Testing

To test the notifications:
1. Get a valid FCM token from your mobile app
2. Run the single notification script first
3. Check if the notification appears on the device
4. Then try batch notifications if needed

## Troubleshooting

**"Invalid FCM token" error:**
- Make sure the token is valid and not expired
- FCM tokens can expire, so your app should refresh them regularly

**"Firebase not initialized" error:**
- Check your Firebase configuration in `src/config/firebase.js`
- Ensure your service account credentials are correct

**No notifications received:**
- Check if the device has notification permissions enabled
- Verify the FCM token is correct
- Make sure the device is connected to the internet

## Customization

You can easily customize:
- **Images**: Edit the `INSTAGRAM_IMAGES` array in the batch script
- **Messages**: Modify the `NOTIFICATION_VARIATIONS` array
- **Rewards**: Change the reward amount in the data payload
- **Styling**: Update the Android/iOS specific styling in the notification service
