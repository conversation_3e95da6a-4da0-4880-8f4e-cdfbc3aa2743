const mongoose = require('mongoose');
const readline = require('readline');
require('dotenv').config();

// Import models
const User = require('../src/models/User');
const Withdrawal = require('../src/models/withdraw');
const Course = require('../src/models/Course');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to ask user for confirmation
function askConfirmation(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase() === 'ok' || answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://yproject85:<EMAIL>/zero_koin';
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    throw error;
  }
}

async function deleteAllUsers() {
  try {
    console.log('🔍 Counting users in database...');
    
    // Count total users
    const userCount = await User.countDocuments();
    const withdrawalCount = await Withdrawal.countDocuments();
    const courseCount = await Course.countDocuments();
    
    console.log(`\n📊 Database Summary:`);
    console.log(`👥 Total Users: ${userCount}`);
    console.log(`📤 Total Withdrawals: ${withdrawalCount}`);
    console.log(`📚 Total Courses: ${courseCount}`);
    
    if (userCount === 0) {
      console.log('\n✅ No users found in database. Nothing to delete.');
      return;
    }
    
    console.log('\n⚠️  WARNING: This will permanently delete ALL users and their data!');
    console.log('⚠️  This action cannot be undone!');
    
    // Ask for confirmation
    const confirmed = await askConfirmation('\n❓ Are you sure you want to delete all users? (Type "ok" to confirm): ');
    
    if (!confirmed) {
      console.log('\n❌ Operation cancelled by user.');
      return;
    }
    
    console.log('\n🗑️  Starting deletion process...');
    
    // Delete all withdrawals first (they reference users)
    console.log('📤 Deleting all withdrawals...');
    const withdrawalResult = await Withdrawal.deleteMany({});
    console.log(`   ✅ Deleted ${withdrawalResult.deletedCount} withdrawals`);
    
    // Delete all courses
    console.log('📚 Deleting all courses...');
    const courseResult = await Course.deleteMany({});
    console.log(`   ✅ Deleted ${courseResult.deletedCount} courses`);
    
    // Finally delete all users
    console.log('👥 Deleting all users...');
    const userResult = await User.deleteMany({});
    console.log(`   ✅ Deleted ${userResult.deletedCount} users`);
    
    console.log('\n🎉 All users and their data have been successfully deleted!');
    console.log(`📊 Summary:`);
    console.log(`   👥 Users deleted: ${userResult.deletedCount}`);
    console.log(`   📤 Withdrawals deleted: ${withdrawalResult.deletedCount}`);
    console.log(`   📚 Courses deleted: ${courseResult.deletedCount}`);
    
  } catch (error) {
    console.error('❌ Error during deletion:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting User Deletion Script...\n');
    
    // Connect to database
    await connectToDatabase();
    
    // Delete all users
    await deleteAllUsers();
    
  } catch (error) {
    console.error('💥 Script execution failed:', error.message);
  } finally {
    // Close readline interface and database connection
    rl.close();
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
    process.exit(0);
  }
}

// Handle script interruption (Ctrl+C)
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Script interrupted by user');
  rl.close();
  await mongoose.disconnect();
  process.exit(0);
});

// Run the script
main();
